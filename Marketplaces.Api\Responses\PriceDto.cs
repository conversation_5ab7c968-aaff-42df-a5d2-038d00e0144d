namespace Marketplaces.Api.Responses;

public class PriceDto
{
    public int Id { get; set; }
    public int? MinAmount { get; set; }
    public string? Name { get; set; }
    public string? Image { get; set; }
    public NestedPriceDto? Ozon { get; set; }
    public NestedPriceDto? Yandex { get; set; }
    public NestedPriceDto? Wildberries { get; set; }
    public decimal? PurchasePrice { get; set; }
    public decimal? MinimumProfitPercentage { get; set; }
    public decimal? MinimumRevenue { get; set; }
    public string? Size { get; set; }

    // Calculated fields for profit analysis
    public decimal? SalePrice { get; set; }
    public decimal? BankCommissionAmount { get; set; }
    public decimal? MarketplaceCommissionAmount { get; set; }
    public decimal? Revenue { get; set; }
    public decimal? Profit { get; set; }
    public decimal? ProfitPercentage { get; set; }
}