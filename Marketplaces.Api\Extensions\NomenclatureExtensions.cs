using Marketplaces.Api.Databases;
using Marketplaces.Api.Models;
using Marketplaces.Api.Responses;

namespace Marketplaces.Api.Extensions;

public static class NomenclatureExtensions
{
    public static void CalculateProfitFields(this PriceDto priceDto, Nomenclature nomenclature, Shop shop)
    {
        // Calculate for each marketplace
        if (priceDto.Ozon != null && nomenclature.Ozon != null)
        {
            var ozonTax = GetMarketplaceTax(shop.OzonMode, shop.OzonTax, shop.OzonEstimatedTax);
            CalculateNestedProfitFields(priceDto.Ozon, nomenclature.PurchasePrice, shop.BankTax, ozonTax);
        }

        if (priceDto.Wildberries != null && nomenclature.Wildberries != null)
        {
            var wbTax = GetMarketplaceTax(shop.WildberriesMode, shop.WildberriesTax, shop.WildberriesEstimatedTax);
            CalculateNestedProfitFields(priceDto.Wildberries, nomenclature.PurchasePrice, shop.BankTax, wbTax);
        }

        if (priceDto.Yandex != null && nomenclature.Yandex != null)
        {
            // Для Yandex пока нет расчетной комиссии, используем только ручную
            CalculateNestedProfitFields(priceDto.Yandex, nomenclature.PurchasePrice, shop.BankTax, shop.YandexTax);
        }
    }

    private static void CalculateNestedProfitFields(NestedPriceDto nestedDto, decimal? purchasePrice, decimal? bankTax, decimal? marketplaceTax)
    {
        if (nestedDto.BasePrice == null || purchasePrice == null || bankTax == null || marketplaceTax == null)
            return;

        // Calculate sale price (BasePrice after discount)
        // BasePrice = Основная цена (650 в примере)
        // DiscountPercent = Процент скидки (79% в примере)
        // SalePrice = BasePrice * (1 - DiscountPercent/100) = 650 * (1 - 0.79) = 650 * 0.21 = 136.5
        var salePrice = nestedDto.BasePrice.Value;
        if (nestedDto.DiscountPercent.HasValue)
        {
            var discountMultiplier = 1 - (nestedDto.DiscountPercent.Value / 100);
            salePrice = nestedDto.BasePrice.Value * discountMultiplier;
        }

        // Calculate bank commission
        // BankCommissionAmount = SalePrice * BankTax/100 = 136.5 * 0.07 = 9.56
        var bankCommissionAmount = salePrice * (bankTax.Value / 100);

        // Calculate marketplace commission
        // MarketplaceCommissionAmount = SalePrice * MarketplaceTax/100 = 136.5 * 0.35 = 47.78
        var marketplaceCommissionAmount = salePrice * (marketplaceTax.Value / 100);

        // Calculate revenue
        // Revenue = SalePrice - BankCommissionAmount - MarketplaceCommissionAmount
        // Revenue = 136.5 - 9.56 - 47.78 = 79.16
        var revenue = salePrice - bankCommissionAmount - marketplaceCommissionAmount;

        // Calculate profit
        // Profit = Revenue - PurchasePrice = 79.16 - 44.2 = 34.96
        var profit = revenue - purchasePrice.Value;

        // Calculate profit percentage
        // ProfitPercentage = (Profit / PurchasePrice) * 100 = (34.96 / 44.2) * 100 = 79.10%
        var profitPercentage = purchasePrice.Value != 0 ? (profit / purchasePrice.Value) * 100 : 0;

        // Set calculated values
        nestedDto.SalePrice = Math.Round(salePrice, 2);
        nestedDto.BankCommissionAmount = Math.Round(bankCommissionAmount, 2);
        nestedDto.MarketplaceCommissionAmount = Math.Round(marketplaceCommissionAmount, 2);
        nestedDto.Revenue = Math.Round(revenue, 2);
        nestedDto.Profit = Math.Round(profit, 2);
        nestedDto.ProfitPercentage = Math.Round(profitPercentage, 2);
    }

    /// <summary>
    /// Получает комиссию маркетплейса в зависимости от режима (ручная или расчетная)
    /// </summary>
    /// <param name="mode">Режим комиссии (Manual или Estimated)</param>
    /// <param name="manualTax">Ручная комиссия</param>
    /// <param name="estimatedTax">Расчетная комиссия</param>
    /// <returns>Комиссия для использования в расчетах</returns>
    private static decimal? GetMarketplaceTax(TaxMode mode, decimal? manualTax, decimal? estimatedTax)
    {
        return mode switch
        {
            TaxMode.Manual => manualTax,
            TaxMode.Estimated => estimatedTax ?? manualTax, // Если расчетной нет, используем ручную как fallback
            _ => manualTax
        };
    }
}
