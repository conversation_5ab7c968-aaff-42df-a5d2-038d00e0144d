using Marketplaces.Api.Databases;
using Marketplaces.Api.Extensions;
using Marketplaces.Api.Models;
using Marketplaces.Api.Responses;
using Marketplaces.Api.Responses.Wildberries;

namespace Marketplaces.Tests;

public class ProfitCalculationTests
{
    [Fact]
    public void CalculateProfitFields_WithWildberriesData_ShouldCalculateCorrectly()
    {
        // Arrange - данные из вашего примера
        var shop = new Shop("Test Shop", "user1")
        {
            BankTax = 7m,                           // 7% комиссия банка
            WildberriesTax = 35m,                   // 35% ручная комиссия маркетплейса
            WildberriesMode = TaxMode.Manual        // Используем ручную комиссию
        };

        var wbNomenclature = new WildberriesNomenclature(1, "Test Product", "TEST001", "SKU001", "M", []);
        wbNomenclature.UpdatePrice(650m);  // Основная цена = 650
        wbNomenclature.UpdateDiscount(79m); // Процент скидки = 79%

        var nomenclature = new Nomenclature(shop.Id)
        {
            PurchasePrice = 44.2m   // Закупка = 44,2
        };
        nomenclature.SetWbNomenclature(wbNomenclature);

        var priceDto = new PriceDto
        {
            Id = 1,
            PurchasePrice = 44.2m,
            Wildberries = new NestedPriceDto
            {
                BasePrice = 650m,       // Основная цена = 650
                DiscountPercent = 79m   // Процент скидки = 79%
            }
        };

        // Act
        priceDto.CalculateProfitFields(nomenclature, shop);

        // Assert
        var wb = priceDto.Wildberries;

        // Цена продажная = Основная цена * (1 - процент скидки) = 650 * (1 - 0.79) = 650 * 0.21 = 136.5
        Assert.Equal(136.5m, wb.SalePrice);

        // Комиссия банка = Цена продажная * процент комиссии банка = 136.5 * 0.07 = 9.56
        Assert.Equal(9.56m, wb.BankCommissionAmount);

        // Комиссия маркетплейса = Цена продажная * процент комиссии маркетплейса = 136.5 * 0.35 = 47.78
        Assert.Equal(47.78m, wb.MarketplaceCommissionAmount);

        // Выручка = Цена продажная - Комиссия банка - Комиссия маркетплейса = 136.5 - 9.56 - 47.78 = 79.16
        Assert.Equal(79.17m, wb.Revenue); // Округление может давать 79.17

        // Прибыль = Выручка - Закупка = 79.17 - 44.2 = 34.97
        Assert.Equal(34.97m, wb.Profit);

        // Процент прибыли = (Прибыль / Закупку) * 100 = (34.97 / 44.2) * 100 = 79.12%
        Assert.Equal(79.12m, wb.ProfitPercentage.Value, 1); // 1 decimal place precision
    }

    [Fact]
    public void CalculateProfitFields_WithoutDiscount_ShouldCalculateCorrectly()
    {
        // Arrange - тест без скидки
        var shop = new Shop("Test Shop", "user1")
        {
            BankTax = 7m,
            OzonTax = 35m
        };

        var ozonNomenclature = new OzonNomenclature(1, "Test Product", "TEST001", "SKU001", null, null, null);
        ozonNomenclature.SetPrice(200m, 200m, 200m, 180m, 220m);

        var nomenclature = new Nomenclature(shop.Id)
        {
            PurchasePrice = 100m
        };
        nomenclature.SetOzonNomenclature(ozonNomenclature);

        var priceDto = new PriceDto
        {
            Id = 1,
            PurchasePrice = 100m,
            Ozon = new NestedPriceDto
            {
                BasePrice = 200m,
                DiscountPercent = null // Без скидки
            }
        };

        // Act
        priceDto.CalculateProfitFields(nomenclature, shop);

        // Assert
        var ozon = priceDto.Ozon;

        // Без скидки цена продажная = основная цена
        Assert.Equal(200m, ozon.SalePrice);

        // Комиссия банка = 200 * 0.07 = 14
        Assert.Equal(14m, ozon.BankCommissionAmount);

        // Комиссия маркетплейса = 200 * 0.35 = 70
        Assert.Equal(70m, ozon.MarketplaceCommissionAmount);

        // Выручка = 200 - 14 - 70 = 116
        Assert.Equal(116m, ozon.Revenue);

        // Прибыль = 116 - 100 = 16
        Assert.Equal(16m, ozon.Profit);

        // Процент прибыли = (16 / 100) * 100 = 16%
        Assert.Equal(16m, ozon.ProfitPercentage);
    }

    [Fact]
    public void CalculateProfitFields_ShouldSelectBestMarketplace()
    {
        // Arrange - несколько маркетплейсов
        var shop = new Shop("Test Shop", "user1")
        {
            BankTax = 7m,
            OzonTax = 30m,
            WildberriesTax = 35m
        };

        var nomenclature = new Nomenclature(shop.Id)
        {
            PurchasePrice = 50m
        };

        var priceDto = new PriceDto
        {
            Id = 1,
            PurchasePrice = 50m,
            Ozon = new NestedPriceDto
            {
                BasePrice = 150m,
                DiscountPercent = null
            },
            Wildberries = new NestedPriceDto
            {
                BasePrice = 200m,
                DiscountPercent = 50m // 50% скидка = 100 рублей продажная цена
            }
        };

        // Act
        priceDto.CalculateProfitFields(nomenclature, shop);

        // Assert
        // Ozon: 150 - (150*0.07) - (150*0.30) = 150 - 10.5 - 45 = 94.5 выручка, 44.5 прибыль
        // WB: 100 - (100*0.07) - (100*0.35) = 100 - 7 - 35 = 58 выручка, 8 прибыль

        // Лучший вариант должен быть Ozon (больше прибыль)
        Assert.Equal(priceDto.Ozon.Profit, priceDto.Profit);
        Assert.Equal(priceDto.Ozon.Revenue, priceDto.Revenue);
    }

    [Fact]
    public void CalculateProfitFields_WithEstimatedTax_ShouldUseEstimatedCommission()
    {
        // Arrange - тест с расчетной комиссией
        var shop = new Shop("Test Shop", "user1")
        {
            BankTax = 7m,
            WildberriesTax = 35m,           // Ручная комиссия 35%
            WildberriesEstimatedTax = 25m,  // Расчетная комиссия 25%
            WildberriesMode = TaxMode.Estimated // Используем расчетную комиссию
        };

        var wbNomenclature = new WildberriesNomenclature(1, "Test Product", "TEST001", "SKU001", "M", []);
        wbNomenclature.UpdatePrice(100m);  // Основная цена = 100
        wbNomenclature.UpdateDiscount(0m); // Без скидки

        var nomenclature = new Nomenclature(shop.Id)
        {
            PurchasePrice = 50m
        };
        nomenclature.SetWbNomenclature(wbNomenclature);

        var priceDto = new PriceDto
        {
            Id = 1,
            PurchasePrice = 50m,
            Wildberries = new NestedPriceDto
            {
                BasePrice = 100m,
                DiscountPercent = 0m
            }
        };

        // Act
        priceDto.CalculateProfitFields(nomenclature, shop);

        // Assert
        var wb = priceDto.Wildberries;

        // Цена продажная = 100 (без скидки)
        Assert.Equal(100m, wb.SalePrice);

        // Комиссия банка = 100 * 0.07 = 7
        Assert.Equal(7m, wb.BankCommissionAmount);

        // Комиссия маркетплейса = 100 * 0.25 = 25 (используется расчетная 25%, а не ручная 35%)
        Assert.Equal(25m, wb.MarketplaceCommissionAmount);

        // Выручка = 100 - 7 - 25 = 68
        Assert.Equal(68m, wb.Revenue);

        // Прибыль = 68 - 50 = 18
        Assert.Equal(18m, wb.Profit);

        // Процент прибыли = (18 / 50) * 100 = 36%
        Assert.Equal(36m, wb.ProfitPercentage);
    }

    [Fact]
    public void CalculateProfitFields_WithEstimatedTaxNull_ShouldFallbackToManual()
    {
        // Arrange - тест когда расчетная комиссия null, должна использоваться ручная
        var shop = new Shop("Test Shop", "user1")
        {
            BankTax = 7m,
            WildberriesTax = 35m,               // Ручная комиссия 35%
            WildberriesEstimatedTax = null,     // Расчетная комиссия отсутствует
            WildberriesMode = TaxMode.Estimated // Режим расчетной комиссии
        };

        var wbNomenclature = new WildberriesNomenclature(1, "Test Product", "TEST001", "SKU001", "M", []);
        wbNomenclature.UpdatePrice(100m);
        wbNomenclature.UpdateDiscount(0m);

        var nomenclature = new Nomenclature(shop.Id)
        {
            PurchasePrice = 50m
        };
        nomenclature.SetWbNomenclature(wbNomenclature);

        var priceDto = new PriceDto
        {
            Id = 1,
            PurchasePrice = 50m,
            Wildberries = new NestedPriceDto
            {
                BasePrice = 100m,
                DiscountPercent = 0m
            }
        };

        // Act
        priceDto.CalculateProfitFields(nomenclature, shop);

        // Assert
        var wb = priceDto.Wildberries;

        // Комиссия маркетплейса = 100 * 0.35 = 35 (fallback к ручной комиссии)
        Assert.Equal(35m, wb.MarketplaceCommissionAmount);

        // Выручка = 100 - 7 - 35 = 58
        Assert.Equal(58m, wb.Revenue);
    }
}
