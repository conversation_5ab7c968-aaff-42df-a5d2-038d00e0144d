using System.Text.Json;
using AutoMapper;
using Marketplaces.Api.Authorization;
using Marketplaces.Api.Clients;
using Marketplaces.Api.Databases;
using Marketplaces.Api.Dtos.Internal;
using Marketplaces.Api.Exceptions;
using Marketplaces.Api.Extensions;
using Marketplaces.Api.Models;
using Marketplaces.Api.Requests;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PriceDto = Marketplaces.Api.Responses.PriceDto;

namespace Marketplaces.Api.Controllers;

[ApiController]
[Route("shops/current/[controller]")]
[Authorize(Policy = Policies.ActiveSubscription)]
public class PricesController : ShopAbstractController
{
    private readonly WildberriesClient _wbClient;
    private readonly OzonClient _ozonClient;
    private readonly YandexClient _yandexClient;
    private readonly IMapper _mapper;
    private readonly ILogger<PricesController> _logger;

    public PricesController(DatabaseContext context,
        WildberriesClient wbClient, OzonClient ozonClient, YandexClient yandexClient, IMapper mapper,
        ILogger<PricesController> logger)
        : base(context)
    {
        _wbClient = wbClient;
        _ozonClient = ozonClient;
        _yandexClient = yandexClient;
        _mapper = mapper;
        _logger = logger;
    }

    [HttpPost("sync")]
    public async Task<List<PriceDto>> SyncPrices()
    {
        // todo: leave only sync part and not return from this place
        var shop = await GetShop();
        var nomenclatures = await Context.FullNomenclatures.Where(n => n.ShopId == shop.Id)
            .OrderBy(n => n.Name)
            .ToListAsync();

        if (shop.ContainsToken(Marketplace.Ozon))
        {
            var ozonNomenclatures = nomenclatures
                .Where(n => n.Ozon != null)
                .Select(n => n.Ozon!).ToList();

            var ozonPrices = await _ozonClient.GetPrices(shop, ozonNomenclatures
                .Select(ozon => ozon.Code).ToList());

            NomenclatureAggregator.UpsertPrices(ozonNomenclatures, ozonPrices);
        }

        var wbAuthenticationData = shop.GetToken(Marketplace.Wildberries);
        if (wbAuthenticationData != null)
        {
            var wbNomenclatures = nomenclatures.Where(n => n.Wildberries != null).ToList();
            var wbPrices = await _wbClient.GetPrices(wbAuthenticationData,
                wbNomenclatures.Select(wb => wb.Wildberries!.Id).ToList());
            NomenclatureAggregator.UpsertPrices(wbNomenclatures, wbPrices);
        }

        var yandexAuthenticationData = shop.GetToken(Marketplace.Yandex);
        if (yandexAuthenticationData != null)
        {
            var yandexNomenclatures = nomenclatures.Where(n => n.Yandex != null).ToList();
            var yandexPrices = await _yandexClient.GetPrices(yandexAuthenticationData, yandexNomenclatures
                .Select(wb => wb.Yandex?.Code).OfType<string>().ToList());
            NomenclatureAggregator.UpsertPrices(yandexNomenclatures, yandexPrices);
        }

        shop.RecordLastPriceSync();
        await Context.SaveChangesAsync();
        var prices = _mapper.Map<List<PriceDto>>(nomenclatures);

        // Calculate profit fields for each price DTO
        for (int i = 0; i < prices.Count; i++)
        {
            var nomenclature = nomenclatures.ElementAtOrDefault(i);
            if (nomenclature != null)
            {
                prices[i].CalculateProfitFields(nomenclature, shop);
            }
        }

        return prices;
    }

    [HttpPost("shapshot")]
    public async Task<IActionResult> MakeSnapshot()
    {
        var shop = await GetShop();
        var nomenclatures = await Context.FullNomenclatures.Where(n => n.ShopId == shop.Id)
            .OrderBy(n => n.Name)
            .ToListAsync();

        var prices = _mapper.Map<List<PriceDto>>(nomenclatures);
        var data = JsonSerializer.Serialize(prices, options: new JsonSerializerOptions()
        {
            WriteIndented = true
        });
        var snapshot = await Context.Set<PriceSnapshot>().FindAsync(shop.Id);
        if (snapshot is null)
        {
            snapshot = new PriceSnapshot(shop.Id, data);
            await Context.AddAsync(snapshot);
        }
        else
        {
            snapshot.Update(data);
        }

        shop.RecordLastPriceSnapshot();
        await Context.SaveChangesAsync();
        return Ok();
    }

    [HttpGet("shapshot")]
    public async Task<IActionResult> GetSnapshot()
    {
        var shop = await GetShop();
        var snapshot = await Context.Set<PriceSnapshot>().FindAsync(shop.Id);
        if (snapshot?.Data is null)
        {
            throw new NotFoundException();
        }

        var nomenclatures = JsonSerializer.Deserialize<List<PriceDto>>(snapshot.Data);
        var dtos = _mapper.Map<List<PriceDto>>(nomenclatures);
        return Ok(dtos);
    }


    [HttpGet]
    public async Task<IActionResult> GetPrices()
    {
        var shop = await GetShop();
        List<PriceDto>? result = null;
        if (shop.LastPriceSyncTime == null || shop.LastPriceSyncTime.Value.Day != DateTimeOffset.UtcNow.Day)
        {
            result = await SyncPrices();
            return Ok(result);
        }

        var nomenclatures = await Context.FullNomenclatures.Where(n => n.ShopId == shop.Id)
            .OrderBy(n => n.Name)
            .ToListAsync();

        await FetchEstimatedTaxes(shop, nomenclatures);

        nomenclatures.LeaveOnlyWithTokens(shop);
        result ??= _mapper.Map<List<PriceDto>>(nomenclatures.SortByWierdRules());

        // Calculate profit fields for each price DTO
        if (result != null)
        {
            for (int i = 0; i < result.Count; i++)
            {
                var nomenclature = nomenclatures.SortByWierdRules().ElementAtOrDefault(i);
                if (nomenclature != null)
                {
                    result[i].CalculateProfitFields(nomenclature, shop);
                }
            }
        }

        return Ok(result);
    }

    private async Task FetchEstimatedTaxes(Shop shop, List<Nomenclature> nomenclatures)
    {
        try
        {
            if (shop.WbKey != null && (shop.LastWildberriesEstimation is null
                                       || shop.LastWildberriesEstimation.Value.Day != DateTimeOffset.UtcNow.Day
                                       || shop.LastWildberriesEstimation.Value.Month != DateTimeOffset.UtcNow.Month))
            {
                var dateTo = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.UtcNow.Day);
                var dateFrom = dateTo.AddMonths(-1).AddDays(-1);
                var wbReport = await _wbClient.GetReportDtos(
                    shop.GetToken(Marketplace.Wildberries),
                    dateFrom,
                    dateTo);

                var wbGeneralCommission = NomenclatureAggregator.UpsertTaxes(nomenclatures, wbReport);
                shop.SetWbEstimatedCommission(wbGeneralCommission);
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to fetch Wildberries estimated taxes");
        }

        try
        {
            if (shop.OzonKey != null && (shop.LastOzonEstimation is null
                || shop.LastOzonEstimation.Value.Day != DateTimeOffset.UtcNow.Day
                || shop.LastOzonEstimation.Value.Month != DateTimeOffset.UtcNow.Month))
            {
                var dateTo = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.UtcNow.Day);
                dateTo = DateTime.SpecifyKind(dateTo, DateTimeKind.Utc);
                var dateFrom = DateTime.SpecifyKind(dateTo.AddMonths(-1), DateTimeKind.Utc);
                var ozonReport = await _ozonClient.GetReportDtos(shop, dateFrom, dateTo);
                var ozonEstimatedCommission = NomenclatureAggregator.UpsertTaxes(nomenclatures, ozonReport);
                shop.SetOzonEstimatedCommission(ozonEstimatedCommission);
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to fetch ozon estimated taxes");
        }

        await Context.SaveChangesAsync();
    }

    [HttpPut]
    public async Task<IActionResult> UpdatePrices([FromBody] UpdatePricesBody body)
    {
        var shop = await GetShop();

        var nomenclatures = await Context.FullNomenclatures
            .Where(n => n.ShopId == shop.Id)
            .ToListAsync();

        var ozonPrices = new List<(string Code, decimal Price)>();
        var wbPrices = new List<(int Id, decimal Discount, decimal Price)>();
        var yandexPrices = new List<(string Code, decimal Price)>();
        foreach (var item in body.Prices)
        {
            var nomenclature = nomenclatures.Find(n => n.Id == item.Id);
            if (nomenclature is null)
            {
                continue;
            }

            nomenclature.UpdatePurchasePrice(item.PurchasePrice);

            if (nomenclature.Ozon != null
                && item.OzonPrice != null
                && item.OzonPrice != nomenclature.Ozon.Price)
            {
                ozonPrices.Add((nomenclature.Ozon.Code, item.OzonPrice.Value));
            }

            if (nomenclature.Wildberries != null
                && (item.WildberriesPrice != nomenclature.Wildberries.Price
                    || item.WildberriesDiscountPercent != nomenclature.Wildberries.Discount))
            {
                // user changes only discount or price
                item.WildberriesDiscountPercent ??= nomenclature.Wildberries.Discount;
                item.WildberriesPrice ??= nomenclature.Wildberries.Price;

                // in case of null values, skip the item
                if (item.WildberriesDiscountPercent is null || item.WildberriesPrice is null)
                {
                    continue;
                }

                wbPrices.Add((nomenclature.Wildberries.Id,
                    item.WildberriesDiscountPercent.Value,
                    item.WildberriesPrice.Value));
                nomenclature.UpdateWildberriesPrice(item.WildberriesPrice.Value);
                nomenclature.UpdateWildberriesDiscount(item.WildberriesDiscountPercent.Value);
            }

            if (nomenclature.Yandex != null
                && item.YandexPrice != null
                && item.YandexPrice != nomenclature.Yandex.Price)
            {
                yandexPrices.Add((nomenclature.Yandex.Code, item.YandexPrice.Value));
                nomenclature.UpdateYandexPrice(item.YandexPrice.Value);
            }
        }

        await Task.WhenAll(
            _ozonClient.UpdatePrice(shop, ozonPrices),
            _wbClient.UpdatePrice(shop.GetToken(Marketplace.Wildberries), wbPrices),
            _yandexClient.UpdatePrice(shop.GetToken(Marketplace.Yandex), yandexPrices)
        );

        await Task.Delay(500);
        await SyncPrices();
        await Context.SaveChangesAsync();
        return Ok();
    }

    [HttpGet("/shops/current/nomenclatures/{nomenclatureId}/ozon/promotes")]
    public async Task<IActionResult> GetPromotesForNomenclature(int nomenclatureId)
    {
        var shop = await GetShop();
        var nomenclature = await Context.FullNomenclatures
            .Where(n => n.ShopId == shop.Id)
            .FirstOrDefaultAsync(n => n.Id == nomenclatureId);

        if (nomenclature?.Ozon is null)
        {
            throw new NotFoundException("Ozon товар не найден");
        }

        var promote = (await _ozonClient.GetPrices(shop, [nomenclature.Ozon.Code])).FirstOrDefault();
        if (promote is null)
        {
            throw new BadRequestException("Промоакции не найдены");
        }

        var promotes = await _ozonClient.GetAvailablePromotes(shop);
        // filter only available promotes
        var promotes1 = promote.MarketingPromotes.Actions.Where(p => promotes.Any(pr => pr.Title == p.Title)).ToList();
        var dtos = _mapper.Map<List<PromoteDto>>(promotes1);
        // take ids from all available promotes
        dtos.ForEach(d => d.Id = promotes.FirstOrDefault(p => p.Title == d.Title)?.Id ?? 0);
        return Ok(dtos);
    }


    [HttpDelete("/shops/current/nomenclatures/{nomenclatureId}/ozon/promotes/{promoteId}")]
    public async Task<IActionResult> DeletePromoteForNomenclature(int nomenclatureId, int promoteId)
    {
        var shop = await GetShop();
        var nomenclature = await Context.FullNomenclatures
            .Where(n => n.ShopId == shop.Id)
            .FirstOrDefaultAsync(n => n.Id == nomenclatureId);

        if (nomenclature?.Ozon is null)
        {
            throw new NotFoundException("Ozon товар не найден");
        }

        await _ozonClient.DeletePromote(shop, nomenclature.Ozon.Id, promoteId);
        await Task.Delay(500);
        var dtos = await SyncPrices();
        var dto = dtos.Find(f => f.Id == nomenclatureId);
        return Ok(dto);
    }
}